"""
Order API Router.
Handles employment order management endpoints.
"""

import logging
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from domains.order.models import (
    EmploymentPickupExpiredRequest, EmploymentPickupExpiredResponse,
    EmploymentPickupRequest, EmploymentPickupResponse,
    EmploymentDeliverRequest, EmploymentDeliverResponse,
    EmploymentSendRequest, EmploymentSendResponse,
    CustomerPickupRequest, CustomerPickupResponse
)
from domains.order.service import order_service
from domains.order.flow_coordinator import flow_coordinator

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/employment/courier/pickup-expired", response_model=EmploymentPickupExpiredResponse)
async def pickup_expired_orders(request: EmploymentPickupExpiredRequest):
    """
    Request to pickup expired orders.
    Creates a WebSocket session and starts pickup loop if expired orders are found.
    """
    try:
        result = await order_service.pickup_expired_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            flow_config = {
                "operation": "pickup_expired",
                "sections": result["sections"],
                "operator_id": request.operator_id
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentPickupExpiredResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start pickup flow",
                    sections=[],
                    total_sections=0
                )
        
        return EmploymentPickupExpiredResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_expired_orders: {e}")
        return EmploymentPickupExpiredResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/courier/pickup", response_model=EmploymentPickupResponse)
async def pickup_employee_orders(request: EmploymentPickupRequest):
    """
    Function for courier to pickup orders from employees.
    Creates a WebSocket session and starts pickup loop if employee orders are found.
    """
    try:
        result = await order_service.pickup_employee_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            flow_config = {
                "operation": "pickup_employee",
                "sections": result["sections"],
                "operator_id": request.operator_id
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentPickupResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start pickup flow",
                    sections=[],
                    total_sections=0
                )
        
        return EmploymentPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_employee_orders: {e}")
        return EmploymentPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/courier/deliver", response_model=EmploymentDeliverResponse)
async def deliver_to_employee(request: EmploymentDeliverRequest):
    """
    Function to deliver orders to employees. This function is for courier.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.deliver_to_employee(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"]:
            flow_config = {
                "operation": "deliver_employee",
                "phone_number": request.phone_number
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentDeliverResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start delivery flow"
                )
        
        return EmploymentDeliverResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in deliver_to_employee: {e}")
        return EmploymentDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/employment/customer/send", response_model=EmploymentSendResponse)
async def employee_send_order(request: EmploymentSendRequest):
    """
    Function for employee to send order.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.employee_send_order(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"] and result["valid"]:
            flow_config = {
                "operation": "employee_send",
                "phone_number": request.phone_number
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return EmploymentSendResponse(
                    session_id=None,
                    success=False,
                    section_id=None,
                    valid=result["valid"],
                    message="Failed to start send flow"
                )
        
        return EmploymentSendResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in employee_send_order: {e}")
        return EmploymentSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/employment/customer/pickup", response_model=CustomerPickupResponse)
async def customer_pickup_order(request: CustomerPickupRequest):
    """
    Function for customer to pickup order using PIN.
    Similar to product pickup - creates WebSocket session and waits for hardware_screen_ready.
    """
    try:
        result = await order_service.customer_pickup_order(request.pickup_pin)
        
        # If order is found, start the flow
        if result["success"]:
            flow_config = {
                "operation": "customer_pickup",
                "section_id": result["section_id"],
                "reservation_id": result.get("reservation_id")
            }
            
            flow_started = await flow_coordinator.start_flow(result["session_id"], flow_config)
            
            if not flow_started:
                return CustomerPickupResponse(
                    session_id=None,
                    success=False,
                    message="Failed to start pickup flow",
                    section_id=None,
                    requires_payment=False,
                    amount=0.0
                )
        
        return CustomerPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in customer_pickup_order: {e}")
        return CustomerPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_id=None,
            requires_payment=False,
            amount=0.0
        )
