"""
Order Step Handlers.
Handles individual steps in order flows.
"""

import logging
import async<PERSON>
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from managers.ws_manager import ws_manager
from hardware.locker_control import <PERSON>r<PERSON><PERSON>roller
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.section_repository import SectionRepository

logger = logging.getLogger(__name__)

class StepHandler(ABC):
    """Base class for step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.logger = logging.getLogger(__name__)
    
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket connection is active"""
        return ws_manager.is_connected(self.session_id)
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False
            
        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            return False
    
    async def wait_for_websocket_ready(self, step_type: str) -> bool:
        """Wait for WebSocket ready signal"""
        try:
            # Send step ready signal
            await self.send_websocket_message({
                "type": "step_ready",
                "step": step_type,
                "message": f"Ready for {step_type}"
            })
            return True
        except Exception as e:
            logger.error(f"Error waiting for WebSocket ready: {e}")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class PickupLoopHandler(StepHandler):
    """Handler for pickup loop operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute pickup loop for multiple sections"""
        try:
            if not await self.wait_for_websocket_ready("pickup_loop"):
                return False
            
            sections = context.get('sections', [])
            operation = context.get('operation', 'pickup')
            
            await self.send_websocket_message({
                "type": "pickup_loop_started",
                "sections": sections,
                "operation": operation,
                "message": f"Starting pickup loop for {len(sections)} sections"
            })
            
            # Start pickup loop
            await self._employment_pickup_loop(sections)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in pickup loop handler: {e}")
            await self.send_websocket_message({
                "type": "pickup_loop_error",
                "message": f"Error in pickup loop: {str(e)}"
            })
            return False
    
    async def _employment_pickup_loop(self, sections: List[int]):
        """Employment pickup loop implementation"""
        picking_up = True
        locker_controller = LockerController()
        
        while picking_up:
            try:
                # Wait for message from WebSocket
                # This is a simplified version - in real implementation you'd need proper message handling
                await asyncio.sleep(0.1)  # Prevent busy waiting
                
                # For now, we'll simulate the pickup process
                # In real implementation, this would wait for actual WebSocket messages
                # and handle section opening/closing based on user input
                
                # Send status update
                await self.send_websocket_message({
                    "type": "pickup_status",
                    "message": "Waiting for section selection",
                    "available_sections": sections
                })
                
                # This is where the actual message handling would go
                # For now, we'll just break after sending the status
                break
                
            except Exception as e:
                logger.error(f"Error in pickup loop: {e}")
                await self.send_websocket_message({
                    "type": "pickup_error",
                    "message": f"Error in pickup: {str(e)}"
                })
                break

class SectionSelectionHandler(StepHandler):
    """Handler for section selection operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute section selection"""
        try:
            if not await self.wait_for_websocket_ready("section_selection"):
                return False

            operation = context.get('operation')
            phone_number = context.get('phone_number')
            reserved_section_id = context.get('reserved_section_id')  # Pre-reserved section from jetveo

            await self.send_websocket_message({
                "type": "section_selection_started",
                "operation": operation,
                "reserved_section_id": reserved_section_id,
                "message": "Starting section selection"
            })

            # If section is already reserved, use it directly, otherwise start selection process
            if reserved_section_id:
                selected_section_id = reserved_section_id
                success = True
            else:
                # Start section selection process
                success, selected_section_id = await self._select_section()

            if success:
                # Create reservation based on operation type
                repo = OrderRepository()
                if operation == "deliver_employee":
                    result = repo.create_employee_delivery_reservation(phone_number, selected_section_id)
                elif operation == "employee_send":
                    result = repo.create_employee_send_reservation(phone_number, selected_section_id)
                else:
                    result = {"success": False, "error": "Unknown operation"}

                if result["success"]:
                    await self.send_websocket_message({
                        "order_deliver" if operation == "deliver_employee" else "order_send": True,
                        "section_id": selected_section_id,
                        "message": "Order processed successfully"
                    })
                else:
                    await self.send_websocket_message({
                        "order_deliver" if operation == "deliver_employee" else "order_send": False,
                        "message": result.get("error", "Failed to create reservation")
                    })
            else:
                await self.send_websocket_message({
                    "order_deliver" if operation == "deliver_employee" else "order_send": False,
                    "message": "Section selection failed"
                })

            return True

        except Exception as e:
            logger.error(f"Error in section selection handler: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False
    
    async def _select_section(self) -> tuple[bool, Optional[int]]:
        """Section selection implementation"""
        selected_section = None
        selecting = True
        locker_controller = LockerController()
        
        while selecting:
            try:
                # Wait for message from WebSocket
                # This is a simplified version - in real implementation you'd need proper message handling
                await asyncio.sleep(0.1)  # Prevent busy waiting
                
                # Send status update
                await self.send_websocket_message({
                    "type": "selection_status",
                    "message": "Waiting for section selection"
                })
                
                # This is where the actual message handling would go
                # For now, we'll simulate a successful selection
                # In real implementation, this would handle actual WebSocket messages
                # for section opening, door state checking, and insertion confirmation
                
                # Simulate successful selection
                selected_section = 1  # This would come from actual user interaction
                selecting = False
                return True, selected_section
                
            except Exception as e:
                logger.error(f"Error in section selection: {e}")
                return False, None

class HardwareHandler(StepHandler):
    """Handler for hardware operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute hardware operation"""
        try:
            if not await self.wait_for_websocket_ready("hardware"):
                return False
            
            section_id = context.get('section_id')
            operation = context.get('operation', 'open_for_pickup')
            reservation_id = context.get('reservation_id')
            
            await self.send_websocket_message({
                "type": "hardware_started",
                "section_id": section_id,
                "operation": operation,
                "message": f"Starting hardware operation for section {section_id}"
            })
            
            # Open the section
            locker_controller = LockerController()
            success = await locker_controller.unlock_locker(section_id, mode="order")
            
            if success:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": True,
                    "section_id": section_id,
                    "message": f"Section {section_id} opened successfully"
                })
                
                # Update reservation status if this is a pickup
                if reservation_id:
                    repo = OrderRepository()
                    repo.update_reservation_status(reservation_id, 0)  # Mark as completed
            else:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": False,
                    "section_id": section_id,
                    "message": f"Failed to open section {section_id}"
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Error in hardware handler: {e}")
            await self.send_websocket_message({
                "type": "hardware_error",
                "message": f"Error in hardware operation: {str(e)}"
            })
            return False

def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "pickup_loop": PickupLoopHandler,
        "section_selection": SectionSelectionHandler,
        "hardware": HardwareHandler
    }
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    return None
